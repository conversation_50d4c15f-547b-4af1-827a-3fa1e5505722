  html.dark {
    .NgxEditor__MenuBar {
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      border-bottom: 1px solid @text-editor-border-color;
      background-color: @dark;
      color: @white;

      .NgxEditor__MenuItem {
        color: @white;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        &.NgxEditor__MenuItem--Active {
          background-color: rgba(255, 255, 255, 0.2);
          color: @white;
        }
      }

      .NgxEditor__Dropdown {
        background-color: @dark;
        color: @white;
        border: 1px solid @text-editor-border-color;

        .NgxEditor__DropdownItem {
          color: @white;

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }

    .NgxEditor {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      border: none;
      background-color: @dark;
      color: @white;

      .ProseMirror {
        background-color: @dark;
        color: @white;
        min-height: calc(10 * 1.5em); // 10 righe con line-height di 1.5em
        padding: 8px 12px;

        &:focus {
          outline: none;
          border: none;
        }

        p {
          color: @white;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          color: @white;
        }

        strong {
          color: @white;
        }

        em {
          color: @white;
        }

        ul,
        ol {
          color: @white;
        }

        blockquote {
          color: @white;
          border-left: 3px solid @medium_grey;
        }

        code {
          background-color: rgba(255, 255, 255, 0.1);
          color: @white;
        }
      }
    }

    .CodeMirror {
      border: 1px solid #363636;
      height: auto;
      margin-bottom: 0.7rem;
      background-color: @dark;
      color: @white;

      pre {
        white-space: pre !important;
        color: @white;
      }

      .CodeMirror-cursor {
        border-left: 1px solid @white;
      }

      .CodeMirror-selected {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }