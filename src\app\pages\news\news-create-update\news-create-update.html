<!-- <PERSON>IN CONTAINER -->
<div class="container">
  <!-- NEWS FORM -->
  <form nz-form class="form" [formGroup]="baseForm" nzLayout="vertical">
    <!-- LOADING SPINNER -->
    <nz-spin [nzSpinning]="loading()">
      <!-- NEWS INFO SECTION -->
      <div nz-row class="w-100" [nzGutter]="16">
        <div nz-col [nzSpan]="12">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <!-- NEWS TITLE -->
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'title'"
                [label]="'NEWS.title' | translate"
                [placeholder]="'NEWS.titlePlaceholder' | translate"
                [minLength]="3"
              ></app-input-generic>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- NEWS SUMMARY -->
              <app-input-textarea
                [parentForm]="baseForm"
                [controlName]="'summary'"
                [label]="'NEWS.summary' | translate"
                [placeholder]="'NEWS.summaryPlaceholder' | translate"
                [minLength]="10"
                [size]="{ minRows: 4.6, maxRows: 20 }"
              ></app-input-textarea>
            </div>
          </div>
        </div>
        <div nz-col [nzSpan]="4">
          <!-- NEWS IMAGE UPLOAD -->
          <app-input-image-upload
            [parentForm]="baseForm"
            [controlName]="'images'"
            [label]="'image'"
            [fileType]="'image/png,image/jpeg'"
            [showAddButton]="FileList?.length === 0"
            [imageContainerSize]="{ width: '100%', height: '200px' }"
            [maxImages]="1"
            [fileList]="FileList || []"
            [imageList]="ImageList || []"
          ></app-input-image-upload>
        </div>
        <div nz-col [nzSpan]="1">
          <nz-divider
            nzType="vertical"
            style="height: 100%; text-align: center"
          ></nz-divider>
        </div>
        <div nz-col [nzSpan]="7">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <div nz-row [nzGutter]="16">
                <div nz-col [nzFlex]="'65px'">
                  <!-- NEWS STATUS -->
                  <a nz-dropdown [nzDropdownMenu]="menu">
                    {{ "NEWS.newsStatus" | translate }}
                    <nz-icon nzType="down" />
                  </a>
                  <nz-dropdown-menu #menu="nzDropdownMenu">
                    <ul nz-menu nzSelectable>
                      <li
                        nz-menu-item
                        (click)="onStatusChange(articleStatusType.draft)"
                        (keyup)="onStatusChange(articleStatusType.draft)"
                        tabindex="0"
                      >
                        {{ "NEWS.draft" | translate }}
                      </li>
                      <li
                        nz-menu-item
                        (click)="onStatusChange(articleStatusType.published)"
                        (keyup)="onStatusChange(articleStatusType.published)"
                        tabindex="1"
                      >
                        {{ "NEWS.published" | translate }}
                      </li>
                    </ul>
                  </nz-dropdown-menu>
                </div>
                @if (crudMode() === crudActionType.create) {
                  <div nz-col [nzFlex]="'auto'">
                    <app-tag-news-status
                      [value]="baseForm.get('status')?.value"
                    ></app-tag-news-status>
                  </div>
                }
              </div>

              @if (crudMode() === crudActionType.update) {
                <div nz-row [nzGutter]="16">
                  <div nz-col [nzSpan]="24">
                    <!-- NEWS FROM -->
                    <app-tag-news-status
                      [value]="articleStatusType.draft"
                    ></app-tag-news-status>
                    <i nz-icon nzType="arrow-right" class="icon-arrow"></i>
                    <!-- NEWS TO -->
                    <app-tag-news-status
                      [value]="baseForm.get('status')?.value"
                    ></app-tag-news-status>
                  </div>
                </div>
              }
            </div>

            <div nz-col [nzSpan]="24">
              <!-- NEWS PINNED -->
              <app-input-checkbox
                [parentForm]="baseForm"
                [controlName]="'pinned'"
                [name]="'NEWS.pinned' | translate"
                [style]="{ marginTop: '24px' }"
              ></app-input-checkbox>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- NEWS DATE -->
              <app-input-single-datepicker
                [parentForm]="baseForm"
                [controlName]="'orderDate'"
                [label]="'NEWS.date'"
                [placeholder]="'NEWS.datePlaceholder'"
                [labelPosition]="'top'"
                [nzFormat]="'yyyy-MM-dd'"
                [style]="{ maxWidth: '200px' }"
              ></app-input-single-datepicker>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- NEWS TAGS -->
              <div nz-row [nzGutter]="[16, 16]" [nzAlign]="'bottom'">
                <div nz-col [nzFlex]="'auto'">
                  <app-input-select
                    [parentForm]="baseForm"
                    [controlName]="'tags'"
                    [label]="'NEWS.tags' | translate"
                    [labelPosition]="'top'"
                    [placeholder]="'NEWS.tagsPlaceholder' | translate"
                    [optionList]="tagsList$()"
                    [configKey]="{ label: 'name', value: 'id' }"
                    [mode]="'multiple'"
                  ></app-input-select>
                </div>
                <div nz-col [nzFlex]="'30px'">
                  <app-simple-button
                    [iconOnly]="true"
                    [icon]="'plus'"
                    (onButtonClick)="openNewTagModal()"
                  ></app-simple-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- NEWS CONTENT SECTION -->
      <div nz-row [nzGutter]="[16, 16]">
        <!-- NEWS CONTENT TITLE -->
        <div nz-col [nzSpan]="24">
          <h3>{{ "NEWS.content" | translate }}</h3>
        </div>
      </div>

      <!-- NEWS CONTENT CARD -->
      <form [formGroup]="baseForm">
        <div
          formArrayName="items"
          cdkDropList
          class="item-list"
          (cdkDropListDropped)="drop($event)"
          cdkDropListOrientation="vertical"
          cdkDropListLockAxis="y"
          [cdkDropListDisabled]="items.controls.length <= 1"
        >
          @for (item of items.controls; track item.value.order) {
            <div nz-row cdkDrag [nzGutter]="[16, 16]" class="item-container">
              <div
                nz-col
                [nzFlex]="'30px'"
                cdkDragHandle
                [style.cursor]="
                  items.controls.length <= 1 ? 'context-menu' : 'move'
                "
              >
                <a class="drag-and-drop">
                  <i nz-icon class="drag-icon" nzType="client-ui:drag-drop"></i>
                </a>
              </div>
              <div nz-col [nzFlex]="'auto'">
                <nz-card [nzTitle]="tplTitle" [nzExtra]="tplExtra">
                  @switch (item.get("type")?.value) {
                    @case (articleItemType.text) {
                      <div [formGroupName]="$index">
                        @if (getEditorForPosition($index); as editor) {
                          <div class="editor">
                            <ngx-editor-menu
                              [editor]="editor"
                              [toolbar]="toolbar()"
                            >
                            </ngx-editor-menu>
                            <ngx-editor
                              [editor]="editor"
                              formControlName="content"
                              [placeholder]="'NEWS.textPlaceholder' | translate"
                            >
                            </ngx-editor>
                          </div>
                        }
                      </div>
                    }
                    @case (articleItemType.images) {
                      <app-input-image-upload
                        [parentForm]="item"
                        [controlName]="'content'"
                        [fileType]="'image/png,image/jpeg'"
                        [showAddButton]="getItemFileList($index)?.length === 0"
                        [imageContainerSize]="{
                          width: '100%',
                          height: '200px',
                        }"
                        [maxImages]="1"
                        [fileList]="getItemFileList($index)"
                        [imageList]="getItemImageList($index)"
                        [customUploadRequest]="getItemUploadRequest($index)"
                        [customRemoveHandler]="getItemRemoveHandler($index)"
                        [customUploadService]="
                          getItemUploadManager($index).uploadService
                        "
                      ></app-input-image-upload>
                    }
                    @case (articleItemType.videos) {
                      <app-input-video-upload
                        [parentForm]="item"
                        [controlName]="'content'"
                        [label]="'video'"
                        [fileType]="
                          'video/mp4,video/mov,video/avi,video/wmv,video/flv,video/mpeg,video/mpg,video/m4v,video/webm,video/ogg,video/3gp,video/3g2,video/mj2'
                        "
                        [showAddButton]="true"
                        [videoContainerSize]="{
                          width: '100%',
                          height: '200px',
                        }"
                        [maxVideos]="1"
                        [fileList]="getItemFileList($index)"
                        [videoList]="getItemVideoList($index)"
                        [customUploadRequest]="
                          getItemVideoUploadRequest($index)
                        "
                        [listType]="'picture-card'"
                        [customRemoveHandler]="getItemRemoveHandler($index)"
                        [customUploadService]="
                          getItemUploadManager($index).uploadService
                        "
                      ></app-input-video-upload>
                    }
                    @default {}
                  }
                </nz-card>
                <ng-template #tplExtra>
                  <a nz-dropdown [nzDropdownMenu]="menu">
                    {{ "actions" | translate }}
                    <nz-icon nzType="down" />
                  </a>
                  <nz-dropdown-menu #menu="nzDropdownMenu">
                    <ul nz-menu>
                      <li nz-menu-item>{{ "edit" | translate }}</li>
                      <li
                        nz-menu-item
                        nzDanger
                        [nzDisabled]="items.length <= 1"
                      >
                        <a
                          (click)="deleteItem($index)"
                          (keyup)="deleteItem($index)"
                          [tabindex]="$index"
                          >{{ "ACTIONS.delete" | translate }}</a
                        >
                      </li>
                    </ul>
                  </nz-dropdown-menu>
                </ng-template>
                <ng-template #tplTitle>
                  <app-input-select
                    [parentForm]="item"
                    [controlName]="'type'"
                    [optionList]="articleItemOptionList()"
                    [style]="{ maxWidth: '200px' }"
                  ></app-input-select>
                </ng-template>
              </div>
            </div>
          }
        </div>
      </form>

      <div nz-row [nzJustify]="'end'" style="margin-top: 16px">
        <app-simple-button
          [title]="'NEWS.addItem' | translate"
          [icon]="'plus'"
          [autoMinify]="false"
          (onButtonClick)="addNewItem()"
        ></app-simple-button>
      </div>
    </nz-spin>
  </form>
</div>

<!-- ACTION BUTTONS TEMPLATE -->
<ng-template #tplButton>
  <!-- SAVE/UPDATE BUTTON -->
  <button
    nz-button
    nzType="primary"
    (click)="onDataSaveClick()"
    [disabled]="!isValidForm() || loading()"
  >
    {{ saveButtonTitle() | translate }}
    @if (crudMode() === crudActionType.create) {
      <i nz-icon nzType="client-ui:plus"></i>
    } @else {
      <i nz-icon nzType="save"></i>
    }
  </button>
</ng-template>
