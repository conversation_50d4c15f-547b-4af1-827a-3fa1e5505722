import { articleItemType, articleStatusType } from '@models/enums/article';
import { IImage } from './image';

export interface INews extends IArticle {
  id: string;
}

export interface IEvent extends IArticle {
  id: string;
  dateRange: [Date, Date];
  location: IGeolocation;
}

export interface IArticle {
  title: string;
  summary?: string;
  images: IImage[];

  url: string;

  status: articleStatusType;
  pinned: boolean;
  tags: ITag[] | string[];
  items: IArticleItem[];

  orderDate: Date;
  createdAt?: Date;
  updatedAt?: Date;
  publishedAt?: Date;
  scheduledAt?: Date;

  // author?: string | IAdmin;
}

export interface IArticleItem {
  type: articleItemType;
  order: number;
  content?: string | IImage[];
}

export interface ITag {
  id?: string;
  name: string;
  news?: IArticle[] | string[];
  count?: number;
}

export interface IGeolocation {
  country: string;
  region: string;
  province: string;
  city: string;
  postalCode: string;
  street: string;
  streetNumber: string;
}
