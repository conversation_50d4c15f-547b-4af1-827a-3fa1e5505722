html.light {
  .NgxEditor__MenuBar {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom: 1px solid @text-editor-border-color;
    background-color: @white;
    color: @black;

    .NgxEditor__MenuItem {
      color: @black;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      &.NgxEditor__MenuItem--Active {
        background-color: rgba(0, 0, 0, 0.1);
        color: @black;
      }
    }

    .NgxEditor__Dropdown {
      background-color: @white;
      color: @black;
      border: 1px solid @text-editor-border-color;

      .NgxEditor__DropdownItem {
        color: @black;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }

  .NgxEditor {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border: none;
    background-color: @white;
    color: @black;

    .ProseMirror {
      background-color: @white;
      color: @black;
      min-height: calc(10 * 1.5em); // 10 righe con line-height di 1.5em
      padding: 8px 12px;
      overflow: auto; // Gestisce il contenuto che potrebbe uscire
      word-wrap: break-word; // Forza il wrap delle parole lunghe

      &:focus {
        outline: none;
        border: none;
      }

      p {
        color: @black;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        color: @black;
      }

      strong {
        color: @black;
      }

      em {
        color: @black;
      }

      ul,
      ol {
        color: @black;
        margin-left: 0;
        padding-left: 1.5em;

        li {
          color: @black;
          margin: 0;
          padding: 0;
          list-style-position: inside;
        }
      }

      blockquote {
        color: @black;
        border-left: 3px solid @strong_grey;
      }

      code {
        background-color: rgba(0, 0, 0, 0.05);
        color: @black;
      }
    }
  }

  .CodeMirror {
    border: 1px solid #d9d9d9;
    height: auto;
    margin-bottom: 0.7rem;
    background-color: @white;
    color: @black;

    pre {
      white-space: pre !important;
      color: @black;
    }

    .CodeMirror-cursor {
      border-left: 1px solid @black;
    }

    .CodeMirror-selected {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
}